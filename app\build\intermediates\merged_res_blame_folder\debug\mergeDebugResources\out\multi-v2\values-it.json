{"logs": [{"outputFile": "com.mtc.addmoney.app-mergeDebugResources-40:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3d977bc618fb655514c04099f530bf8d\\transformed\\core-1.13.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3448,3546,3648,3747,3849,3958,4065,10011", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3541,3643,3742,3844,3953,4060,4190,10107"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\97f871a1db6fc59707f351b418b00543\\transformed\\appcompat-1.7.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,9683", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,9760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37f7566d407a97696d5c58add443eebd\\transformed\\material-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1090,1156,1251,1336,1398,1486,1548,1617,1680,1753,1816,1870,1991,2048,2110,2164,2241,2378,2463,2543,2642,2728,2810,2945,3026,3107,3253,3344,3434,3489,3540,3606,3679,3759,3830,3910,3985,4062,4131,4208,4313,4401,4490,4583,4676,4750,4830,4924,4975,5059,5125,5209,5297,5359,5423,5486,5554,5669,5783,5889,5998,6057,6112,6192,6277,6356", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "263,347,428,505,604,699,798,938,1021,1085,1151,1246,1331,1393,1481,1543,1612,1675,1748,1811,1865,1986,2043,2105,2159,2236,2373,2458,2538,2637,2723,2805,2940,3021,3102,3248,3339,3429,3484,3535,3601,3674,3754,3825,3905,3980,4057,4126,4203,4308,4396,4485,4578,4671,4745,4825,4919,4970,5054,5120,5204,5292,5354,5418,5481,5549,5664,5778,5884,5993,6052,6107,6187,6272,6351,6433"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3012,3096,3177,3254,3353,4195,4294,4434,4517,4581,4647,4742,4827,4889,4977,5039,5108,5171,5244,5307,5361,5482,5539,5601,5655,5732,5869,5954,6034,6133,6219,6301,6436,6517,6598,6744,6835,6925,6980,7031,7097,7170,7250,7321,7401,7476,7553,7622,7699,7804,7892,7981,8074,8167,8241,8321,8415,8466,8550,8616,8700,8788,8850,8914,8977,9045,9160,9274,9380,9489,9548,9603,9765,9850,9929", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,83,80,76,98,94,98,139,82,63,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,79,98,85,81,134,80,80,145,90,89,54,50,65,72,79,70,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79,84,78,81", "endOffsets": "313,3091,3172,3249,3348,3443,4289,4429,4512,4576,4642,4737,4822,4884,4972,5034,5103,5166,5239,5302,5356,5477,5534,5596,5650,5727,5864,5949,6029,6128,6214,6296,6431,6512,6593,6739,6830,6920,6975,7026,7092,7165,7245,7316,7396,7471,7548,7617,7694,7799,7887,7976,8069,8162,8236,8316,8410,8461,8545,8611,8695,8783,8845,8909,8972,9040,9155,9269,9375,9484,9543,9598,9678,9845,9924,10006"}}]}]}