package com.mtc.addmoney;


import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import java.util.ArrayList;
import java.util.Map;
import org.json.JSONException;
import org.json.JSONObject;

/* loaded from: classes2.dex */
public class ForwardingConfig {
    private static final String KEY_HEADERS = "headers";
    private static final String KEY_IGNORE_SSL = "ignore_ssl";
    private static final String KEY_TEMPLATE = "template";
    private static final String KEY_URL = "url";
    private final Context context;
    private String headers;
    private boolean ignoreSsl = false;
    private String sender;
    private String template;
    private String url;

    public static String getDefaultJsonHeaders() {
        return "{\"User-agent\":\"SMS Forwarder App\"}";
    }

    public static String getDefaultJsonTemplate() {
        return "{\n  \"from\":\"%from%\",\n  \"text\":\"%text%\",\n  \"sentStamp\":%sentStamp%,\n  \"receivedStamp\":%receivedStamp%,\n  \"sim\":\"%sim%\"\n}";
    }

    public ForwardingConfig(Context context) {
        this.context = context;
    }

    public String getSender() {
        return this.sender;
    }

    public void setSender(String str) {
        this.sender = str;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String str) {
        this.url = str;
    }

    public String getTemplate() {
        return this.template;
    }

    public void setTemplate(String str) {
        this.template = str;
    }

    public String getHeaders() {
        return this.headers;
    }

    public void setHeaders(String str) {
        this.headers = str;
    }

    public boolean getIgnoreSsl() {
        return this.ignoreSsl;
    }

    public void setIgnoreSsl(boolean z) {
        this.ignoreSsl = z;
    }

    public void save() throws JSONException {
        try {
            JSONObject jSONObject = new JSONObject();
            jSONObject.put("url", this.url);
            jSONObject.put(KEY_TEMPLATE, this.template);
            jSONObject.put(KEY_HEADERS, this.headers);
            jSONObject.put(KEY_IGNORE_SSL, this.ignoreSsl);
            SharedPreferences.Editor editor = getEditor(this.context);
            editor.putString(this.sender, jSONObject.toString());
            editor.commit();
        } catch (Exception e) {
            Log.e("ForwardingConfig", e.getMessage());
        }
    }

    public static ArrayList<ForwardingConfig> getAll(Context context) {
        Map<String, ?> all = getPreference(context).getAll();
        ArrayList<ForwardingConfig> arrayList = new ArrayList<>();
        for (Map.Entry<String, ?> entry : all.entrySet()) {
            ForwardingConfig forwardingConfig = new ForwardingConfig(context);
            forwardingConfig.setSender(entry.getKey());
            String str = (String) entry.getValue();
            if (str.charAt(0) == '{') {
                try {
                    JSONObject jSONObject = new JSONObject(str);
                    forwardingConfig.setUrl(jSONObject.getString("url"));
                    forwardingConfig.setTemplate(jSONObject.getString(KEY_TEMPLATE));
                    forwardingConfig.setHeaders(jSONObject.getString(KEY_HEADERS));
                    try {
                        forwardingConfig.setIgnoreSsl(jSONObject.getBoolean(KEY_IGNORE_SSL));
                    } catch (JSONException unused) {
                    }
                } catch (JSONException e) {
                    Log.e("ForwardingConfig", e.getMessage());
                }
            } else {
                forwardingConfig.setUrl(str);
                forwardingConfig.setTemplate(getDefaultJsonTemplate());
                forwardingConfig.setHeaders(getDefaultJsonHeaders());
            }
            arrayList.add(forwardingConfig);
        }
        return arrayList;
    }

    public void remove() {
        SharedPreferences.Editor editor = getEditor(this.context);
        editor.remove(getSender());
        editor.commit();
    }

    private static SharedPreferences getPreference(Context context) {
        return context.getSharedPreferences(context.getString(R.string.key_phones_preference), 0);
    }

    private static SharedPreferences.Editor getEditor(Context context) {
        return getPreference(context).edit();
    }
}