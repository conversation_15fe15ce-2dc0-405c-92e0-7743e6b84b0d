{"logs": [{"outputFile": "com.mtc.addmoney.app-mergeDebugResources-40:/values-bg/values-bg.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\3d977bc618fb655514c04099f530bf8d\\transformed\\core-1.13.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3537,3634,3744,3846,3947,4054,4159,10078", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "3629,3739,3841,3942,4049,4154,4273,10174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\97f871a1db6fc59707f351b418b00543\\transformed\\appcompat-1.7.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,436,522,632,753,833,910,1001,1094,1189,1283,1383,1476,1571,1679,1770,1861,1944,2058,2166,2266,2380,2487,2595,2755,2854", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "220,326,431,517,627,748,828,905,996,1089,1184,1278,1378,1471,1566,1674,1765,1856,1939,2053,2161,2261,2375,2482,2590,2750,2849,2933"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,435,541,646,732,842,963,1043,1120,1211,1304,1399,1493,1593,1686,1781,1889,1980,2071,2154,2268,2376,2476,2590,2697,2805,2965,9748", "endColumns": "119,105,104,85,109,120,79,76,90,92,94,93,99,92,94,107,90,90,82,113,107,99,113,106,107,159,98,83", "endOffsets": "430,536,641,727,837,958,1038,1115,1206,1299,1394,1488,1588,1681,1776,1884,1975,2066,2149,2263,2371,2471,2585,2692,2800,2960,3059,9827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\37f7566d407a97696d5c58add443eebd\\transformed\\material-1.12.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,423,506,628,738,833,966,1055,1118,1184,1281,1361,1423,1512,1575,1640,1699,1772,1835,1889,2017,2074,2136,2190,2263,2406,2490,2568,2661,2743,2831,2967,3055,3143,3279,3364,3441,3494,3545,3611,3686,3762,3833,3912,3989,4065,4142,4216,4328,4419,4494,4585,4677,4751,4838,4929,4984,5066,5132,5215,5301,5363,5427,5490,5560,5677,5789,5900,6010,6067,6122,6208,6299,6375", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "260,339,418,501,623,733,828,961,1050,1113,1179,1276,1356,1418,1507,1570,1635,1694,1767,1830,1884,2012,2069,2131,2185,2258,2401,2485,2563,2656,2738,2826,2962,3050,3138,3274,3359,3436,3489,3540,3606,3681,3757,3828,3907,3984,4060,4137,4211,4323,4414,4489,4580,4672,4746,4833,4924,4979,5061,5127,5210,5296,5358,5422,5485,5555,5672,5784,5895,6005,6062,6117,6203,6294,6370,6449"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3064,3143,3222,3305,3427,4278,4373,4506,4595,4658,4724,4821,4901,4963,5052,5115,5180,5239,5312,5375,5429,5557,5614,5676,5730,5803,5946,6030,6108,6201,6283,6371,6507,6595,6683,6819,6904,6981,7034,7085,7151,7226,7302,7373,7452,7529,7605,7682,7756,7868,7959,8034,8125,8217,8291,8378,8469,8524,8606,8672,8755,8841,8903,8967,9030,9100,9217,9329,9440,9550,9607,9662,9832,9923,9999", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,78,78,82,121,109,94,132,88,62,65,96,79,61,88,62,64,58,72,62,53,127,56,61,53,72,142,83,77,92,81,87,135,87,87,135,84,76,52,50,65,74,75,70,78,76,75,76,73,111,90,74,90,91,73,86,90,54,81,65,82,85,61,63,62,69,116,111,110,109,56,54,85,90,75,78", "endOffsets": "310,3138,3217,3300,3422,3532,4368,4501,4590,4653,4719,4816,4896,4958,5047,5110,5175,5234,5307,5370,5424,5552,5609,5671,5725,5798,5941,6025,6103,6196,6278,6366,6502,6590,6678,6814,6899,6976,7029,7080,7146,7221,7297,7368,7447,7524,7600,7677,7751,7863,7954,8029,8120,8212,8286,8373,8464,8519,8601,8667,8750,8836,8898,8962,9025,9095,9212,9324,9435,9545,9602,9657,9743,9918,9994,10073"}}]}]}