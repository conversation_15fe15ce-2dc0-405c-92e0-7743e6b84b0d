package com.mtc.addmoney;

import android.app.ActivityManager;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;

public class MainActivity extends AppCompatActivity {

    private static final int PERMISSION_CODE = 0;
    private static final int NOTIFICATION_PERMISSION_CODE = 1;
    private Context context;
    private AlertDialog alertDialog;

    // Tab-related components
    private TabLayout tabLayout;
    private ViewPager2 viewPager;
    private MainPagerAdapter pagerAdapter;
    private FloatingActionButton btnAdd;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main_with_tabs);

        context = this;

        // Setup Toolbar as ActionBar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        toolbar.setTitleTextColor(getResources().getColor(android.R.color.white));

        setupTabs();
        setupFloatingActionButton();

        // Check permissions
        checkPermissions();
    }

    private void checkPermissions() {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.RECEIVE_SMS)
                != android.content.pm.PackageManager.PERMISSION_GRANTED ||
                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
                        ContextCompat.checkSelfPermission(this, android.Manifest.permission.POST_NOTIFICATIONS)
                                != android.content.pm.PackageManager.PERMISSION_GRANTED)) {

            String[] permissions = new String[]{android.Manifest.permission.RECEIVE_SMS};
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                permissions = new String[]{android.Manifest.permission.RECEIVE_SMS, android.Manifest.permission.POST_NOTIFICATIONS};
            }
            ActivityCompat.requestPermissions(this, permissions, PERMISSION_CODE);
        } else {
            setupAfterPermissions();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == PERMISSION_CODE) {
            boolean allGranted = true;
            for (int i = 0; i < permissions.length; i++) {
                if (android.Manifest.permission.RECEIVE_SMS.equals(permissions[i]) ||
                        (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU &&
                                android.Manifest.permission.POST_NOTIFICATIONS.equals(permissions[i]))) {
                    if (grantResults[i] != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                        allGranted = false;
                        if (android.Manifest.permission.RECEIVE_SMS.equals(permissions[i])) {
                            showInfo(getString(R.string.permission_needed));
                        } else if (android.Manifest.permission.POST_NOTIFICATIONS.equals(permissions[i])) {
                            showInfo(getString(R.string.notification_permission_needed));
                        }
                        break;
                    }
                }
            }
            if (allGranted) {
                setupAfterPermissions();
            }
        } else {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    private void setupTabs() {
        tabLayout = findViewById(R.id.tab_layout);
        viewPager = findViewById(R.id.view_pager);

        pagerAdapter = new MainPagerAdapter(this);
        viewPager.setAdapter(pagerAdapter);

        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            switch (position) {
                case MainPagerAdapter.TAB_WEBHOOKS:
                    tab.setText(getString(R.string.tab_webhooks));
                    break;
                case MainPagerAdapter.TAB_MONITOR:
                    tab.setText(getString(R.string.tab_monitor));
                    break;
            }
        }).attach();
    }

    private void setupFloatingActionButton() {
        btnAdd = findViewById(R.id.btn_add);
        btnAdd.setOnClickListener(v -> showAddDialog());
    }

    private void setupAfterPermissions() {
        showInfo("");
        if (!isServiceRunning()) {
            startService();
        }
    }

    private boolean isServiceRunning() {
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager == null) return false;

        for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
            if (SmsReceiverService.class.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    private void startService() {
        Intent intent = new Intent(this, SmsReceiverService.class);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            getApplicationContext().startForegroundService(intent);
        } else {
            getApplicationContext().startService(intent);
        }
    }

    private void showInfo(String message) {
        TextView infoNotice = findViewById(R.id.info_notice);
        infoNotice.setText(message);
    }

    private void showAddDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        View dialogView = getLayoutInflater().inflate(R.layout.dialog_add, null);

        EditText inputPhone = dialogView.findViewById(R.id.input_phone);
        EditText inputUrl = dialogView.findViewById(R.id.input_url);
        EditText inputJsonTemplate = dialogView.findViewById(R.id.input_json_template);
        EditText inputJsonHeaders = dialogView.findViewById(R.id.input_json_headers);
        CheckBox inputIgnoreSsl = dialogView.findViewById(R.id.input_ignore_ssl);
        Button btnAdd = dialogView.findViewById(R.id.btn_add);
        Button btnCancel = dialogView.findViewById(R.id.btn_cancel);

        inputJsonTemplate.setText(ForwardingConfig.getDefaultJsonTemplate());
        inputJsonHeaders.setText(ForwardingConfig.getDefaultJsonHeaders());

        builder.setView(dialogView);

        AlertDialog alertDialog = builder.create();
        alertDialog.getWindow().setSoftInputMode(16);
        alertDialog.getWindow().setBackgroundDrawableResource(android.R.color.transparent);

        alertDialog.show();

        btnAdd.setOnClickListener(v -> {
            try {
                String sender = inputPhone.getText().toString().trim();
                if (TextUtils.isEmpty(sender)) {
                    inputPhone.setError(getString(R.string.error_empty_sender));
                    return;
                }

                String urlStr = inputUrl.getText().toString().trim();
                if (TextUtils.isEmpty(urlStr)) {
                    inputUrl.setError(getString(R.string.error_empty_url));
                    return;
                }
                new URL(urlStr); // Validate URL format

                String jsonTemplate = inputJsonTemplate.getText().toString().trim();
                try {
                    new JSONObject(jsonTemplate); // Validate JSON template
                } catch (JSONException e) {
                    inputJsonTemplate.setError(getString(R.string.error_wrong_json));
                    return;
                }

                String jsonHeaders = inputJsonHeaders.getText().toString().trim();
                try {
                    new JSONObject(jsonHeaders); // Validate JSON headers
                } catch (JSONException e) {
                    inputJsonHeaders.setError(getString(R.string.error_wrong_json));
                    return;
                }

                boolean ignoreSsl = inputIgnoreSsl.isChecked();

                ForwardingConfig forwardingConfig = new ForwardingConfig(context);
                forwardingConfig.setSender(sender);
                forwardingConfig.setUrl(urlStr);
                forwardingConfig.setTemplate(jsonTemplate);
                forwardingConfig.setHeaders(jsonHeaders);
                forwardingConfig.setIgnoreSsl(ignoreSsl);
                forwardingConfig.save();

                // Refresh the webhooks fragment
                if (pagerAdapter != null && pagerAdapter.getWebhooksFragment() != null) {
                    pagerAdapter.getWebhooksFragment().refreshList();
                }

                alertDialog.dismiss();

            } catch (MalformedURLException e) {
                inputUrl.setError(getString(R.string.error_wrong_url));
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        });

        btnCancel.setOnClickListener(v -> alertDialog.dismiss());
    }
}