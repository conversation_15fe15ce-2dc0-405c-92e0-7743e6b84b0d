1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.mtc.addmoney"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!-- Permissions -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:6:5-79
12-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.RECEIVE_SMS" />
13-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:7:5-70
13-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:7:22-67
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
14-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:8:5-77
14-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:8:22-74
15    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
15-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:9:5-81
15-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:10:5-68
16-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:11:22-74
18
19    <permission
19-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:12:5-14:47
20        android:name="com.mtc.addmoney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:13:9-81
21        android:protectionLevel="signature" />
21-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:14:9-44
22
23    <uses-feature
23-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:15:5-17:36
24        android:name="android.hardware.telephony"
24-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:16:9-50
25        android:required="false" />
25-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:17:9-33
26
27    <uses-permission android:name="com.mtc.addmoney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
27-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:18:5-97
27-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:18:22-94
28
29    <application
29-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:19:5-198:19
30        android:allowBackup="true"
30-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:20:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:28:9-77
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:21:9-65
33        android:debuggable="true"
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:22:9-54
36        android:icon="@mipmap/ic_launcher"
36-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:23:9-43
37        android:label="@string/app_name"
37-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:24:9-41
38        android:roundIcon="@mipmap/ic_launcher_round"
38-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:25:9-54
39        android:supportsRtl="true"
39-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:26:9-35
40        android:testOnly="true"
41        android:theme="@style/Theme.MtcAddMoney"
41-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:27:9-49
42        android:usesCleartextTraffic="true" >
42-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:29:9-44
43
44        <!-- Main Activity -->
45        <activity
45-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:34:9-41:20
46            android:name="com.mtc.addmoney.MainActivity"
46-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:35:13-41
47            android:exported="true" >
47-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:36:13-36
48            <intent-filter>
48-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:37:13-40:29
49                <action android:name="android.intent.action.MAIN" />
49-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:38:17-69
49-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:38:25-66
50
51                <category android:name="android.intent.category.LAUNCHER" />
51-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:39:17-77
51-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:39:27-74
52            </intent-filter>
53        </activity>
54
55        <!-- SmsReceiverService -->
56        <service
56-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:44:9-47:39
57            android:name="com.mtc.addmoney.SmsReceiverService"
57-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:45:13-63
58            android:enabled="true"
58-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:46:13-35
59            android:exported="true" />
59-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:47:13-36
60
61        <!-- Boot Completed Receiver -->
62        <receiver
62-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:50:9-57:20
63            android:name="com.mtc.addmoney.BootCompletedReceiver"
63-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:51:13-66
64            android:enabled="true"
64-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:52:13-35
65            android:exported="true" >
65-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:53:13-36
66            <intent-filter>
66-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:54:13-56:29
67                <action android:name="android.intent.action.BOOT_COMPLETED" />
67-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:55:17-79
67-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:55:25-76
68            </intent-filter>
69        </receiver>
70
71        <!-- AndroidX Startup Providers and WorkManager services -->
72        <provider
72-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:60:9-68:20
73            android:name="androidx.startup.InitializationProvider"
73-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:61:13-67
74            android:authorities="com.mtc.addmoney.androidx-startup"
74-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:63:13-68
75            android:exported="false" >
75-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:62:13-37
76            <meta-data
76-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:64:13-111
77                android:name="androidx.work.WorkManagerInitializer"
77-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:64:24-75
78                android:value="androidx.startup" />
78-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:64:76-108
79            <meta-data
79-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:65:13-118
80                android:name="androidx.emoji2.text.EmojiCompatInitializer"
80-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:65:24-82
81                android:value="androidx.startup" />
81-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:65:83-115
82            <meta-data
82-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:66:13-121
83                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
83-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:66:24-85
84                android:value="androidx.startup" />
84-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:66:86-118
85            <meta-data
85-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:67:13-128
86                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
86-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:67:24-92
87                android:value="androidx.startup" />
87-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:67:93-125
88        </provider>
89
90        <service
90-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:70:9-74:47
91            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
91-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:71:13-88
92            android:directBootAware="false"
92-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:74:13-44
93            android:enabled="@bool/enable_system_alarm_service_default"
93-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:72:13-72
94            android:exported="false" />
94-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:73:13-37
95        <service
95-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:76:9-81:47
96            android:name="androidx.work.impl.background.systemjob.SystemJobService"
96-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:77:13-84
97            android:directBootAware="false"
97-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:81:13-44
98            android:enabled="@bool/enable_system_job_service_default"
98-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:79:13-70
99            android:exported="true"
99-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:80:13-36
100            android:permission="android.permission.BIND_JOB_SERVICE" />
100-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:78:13-69
101        <service
101-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:83:9-87:47
102            android:name="androidx.work.impl.foreground.SystemForegroundService"
102-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:84:13-81
103            android:directBootAware="false"
103-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:87:13-44
104            android:enabled="@bool/enable_system_foreground_service_default"
104-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:85:13-77
105            android:exported="false" />
105-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:86:13-37
106
107        <!-- WorkManager related receivers -->
108        <receiver
108-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:90:9-95:50
109            android:name="androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver"
109-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:91:13-88
110            android:directBootAware="false"
110-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:94:13-44
111            android:enabled="true"
111-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:92:13-35
112            android:exported="false" />
112-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:93:13-37
113        <receiver
113-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:97:9-106:20
114            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy"
114-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:98:13-106
115            android:directBootAware="false"
115-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:101:13-44
116            android:enabled="false"
116-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:99:13-36
117            android:exported="false" >
117-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:100:13-37
118            <intent-filter>
118-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:102:13-105:29
119                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
119-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:103:17-87
119-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:103:25-84
120                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
120-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:104:17-90
120-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:104:25-87
121            </intent-filter>
122        </receiver>
123        <receiver
123-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:108:9-117:20
124            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy"
124-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:109:13-104
125            android:directBootAware="false"
125-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:112:13-44
126            android:enabled="false"
126-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:110:13-36
127            android:exported="false" >
127-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:111:13-37
128            <intent-filter>
128-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:113:13-116:29
129                <action android:name="android.intent.action.BATTERY_OKAY" />
129-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:114:17-77
129-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:114:25-74
130                <action android:name="android.intent.action.BATTERY_LOW" />
130-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:115:17-76
130-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:115:25-73
131            </intent-filter>
132        </receiver>
133        <receiver
133-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:119:9-128:20
134            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy"
134-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:120:13-104
135            android:directBootAware="false"
135-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:123:13-44
136            android:enabled="false"
136-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:121:13-36
137            android:exported="false" >
137-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:122:13-37
138            <intent-filter>
138-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:124:13-127:29
139                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
139-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:125:17-83
139-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:125:25-80
140                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
140-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:126:17-82
140-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:126:25-79
141            </intent-filter>
142        </receiver>
143        <receiver
143-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:130:9-138:20
144            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy"
144-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:131:13-103
145            android:directBootAware="false"
145-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:134:13-44
146            android:enabled="false"
146-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:132:13-36
147            android:exported="false" >
147-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:133:13-37
148            <intent-filter>
148-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:135:13-137:29
149                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
149-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:136:17-79
149-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:136:25-76
150            </intent-filter>
151        </receiver>
152        <receiver
152-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:140:9-150:20
153            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
153-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:141:13-88
154            android:directBootAware="false"
154-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:144:13-44
155            android:enabled="false"
155-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:142:13-36
156            android:exported="false" >
156-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:143:13-37
157            <intent-filter>
157-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:145:13-149:29
158                <action android:name="android.intent.action.BOOT_COMPLETED" />
158-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:55:17-79
158-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:55:25-76
159                <action android:name="android.intent.action.TIME_SET" />
159-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:147:17-73
159-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:147:25-70
160                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
160-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:148:17-81
160-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:148:25-78
161            </intent-filter>
162        </receiver>
163        <receiver
163-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:152:9-160:20
164            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
164-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:153:13-99
165            android:directBootAware="false"
165-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:156:13-44
166            android:enabled="@bool/enable_system_alarm_service_default"
166-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:154:13-72
167            android:exported="false" >
167-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:155:13-37
168            <intent-filter>
168-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:157:13-159:29
169                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
169-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:158:17-98
169-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:158:25-95
170            </intent-filter>
171        </receiver>
172        <receiver
172-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:162:9-171:20
173            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
173-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:163:13-78
174            android:directBootAware="false"
174-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:167:13-44
175            android:enabled="true"
175-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:165:13-35
176            android:exported="true"
176-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:166:13-36
177            android:permission="android.permission.DUMP" >
177-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:164:13-57
178            <intent-filter>
178-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:168:13-170:29
179                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
179-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:169:17-88
179-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:169:25-85
180            </intent-filter>
181        </receiver>
182
183        <!-- Optional libraries -->
184        <uses-library
184-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:174:9-92
185            android:name="androidx.window.extensions"
185-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:174:23-64
186            android:required="false" />
186-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:174:65-89
187        <uses-library
187-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:175:9-89
188            android:name="androidx.window.sidecar"
188-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:175:23-61
189            android:required="false" />
189-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:175:62-86
190
191        <!-- Room multi-instance invalidation service -->
192        <service
192-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:178:9-181:46
193            android:name="androidx.room.MultiInstanceInvalidationService"
193-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:179:13-74
194            android:directBootAware="true"
194-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:181:13-43
195            android:exported="false" />
195-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:180:13-37
196
197        <!-- Profile Installer Receiver -->
198        <receiver
198-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:184:9-196:20
199            android:name="androidx.profileinstaller.ProfileInstallReceiver"
199-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:185:13-76
200            android:directBootAware="false"
200-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:189:13-44
201            android:enabled="true"
201-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:187:13-35
202            android:exported="true"
202-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:188:13-36
203            android:permission="android.permission.DUMP" >
203-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:186:13-57
204            <intent-filter>
204-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:190:13-195:29
205                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
205-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:191:17-91
205-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:191:25-88
206                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
206-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:192:17-85
206-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:192:25-82
207                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
207-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:193:17-88
207-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:193:25-85
208                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
208-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:194:17-95
208-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:194:25-92
209            </intent-filter>
210            <intent-filter>
210-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
211                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
211-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:191:17-91
211-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:191:25-88
212            </intent-filter>
213            <intent-filter>
213-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
214                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
214-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:192:17-85
214-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:192:25-82
215            </intent-filter>
216            <intent-filter>
216-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
217                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
217-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:193:17-88
217-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:193:25-85
218            </intent-filter>
219            <intent-filter>
219-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
220                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
220-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:194:17-95
220-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:194:25-92
221            </intent-filter>
222        </receiver>
223        <receiver
223-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:61:9-66:35
224            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
224-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:62:13-88
225            android:directBootAware="false"
225-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:63:13-44
226            android:enabled="true"
226-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:64:13-35
227            android:exported="false" />
227-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:65:13-37
228        <receiver
228-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:67:9-77:20
229            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
229-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:68:13-106
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:69:13-44
231            android:enabled="false"
231-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:70:13-36
232            android:exported="false" >
232-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:71:13-37
233            <intent-filter>
233-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:102:13-105:29
234                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
234-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:103:17-87
234-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:103:25-84
235                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
235-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:104:17-90
235-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:104:25-87
236            </intent-filter>
237        </receiver>
238        <receiver
238-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:78:9-88:20
239            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
239-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:79:13-104
240            android:directBootAware="false"
240-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:80:13-44
241            android:enabled="false"
241-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:81:13-36
242            android:exported="false" >
242-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:82:13-37
243            <intent-filter>
243-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:113:13-116:29
244                <action android:name="android.intent.action.BATTERY_OKAY" />
244-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:114:17-77
244-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:114:25-74
245                <action android:name="android.intent.action.BATTERY_LOW" />
245-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:115:17-76
245-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:115:25-73
246            </intent-filter>
247        </receiver>
248        <receiver
248-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:89:9-99:20
249            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
249-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:90:13-104
250            android:directBootAware="false"
250-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:91:13-44
251            android:enabled="false"
251-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:92:13-36
252            android:exported="false" >
252-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:93:13-37
253            <intent-filter>
253-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:124:13-127:29
254                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
254-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:125:17-83
254-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:125:25-80
255                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
255-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:126:17-82
255-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:126:25-79
256            </intent-filter>
257        </receiver>
258        <receiver
258-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:100:9-109:20
259            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
259-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:101:13-103
260            android:directBootAware="false"
260-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:102:13-44
261            android:enabled="false"
261-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:103:13-36
262            android:exported="false" >
262-->[androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:104:13-37
263            <intent-filter>
263-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:135:13-137:29
264                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
264-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:136:17-79
264-->C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:136:25-76
265            </intent-filter>
266        </receiver>
267    </application>
268
269</manifest>
