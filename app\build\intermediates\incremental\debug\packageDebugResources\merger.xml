<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res"><file name="ic_btn_add" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\drawable\ic_btn_add.png" qualifiers="" type="drawable"/><file name="ic_f" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\drawable\ic_f.png" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="abc_decor_view_status_guard">#ff000000</color><color name="abc_decor_view_status_guard_light">#ffffffff</color><color name="abc_search_url_text_normal">#7fa87f</color><color name="abc_search_url_text_pressed">@android:color/black</color><color name="abc_search_url_text_selected">@android:color/black</color><color name="accent_material_dark">@color/material_deep_teal_200</color><color name="accent_material_light">@color/material_deep_teal_500</color><color name="androidx_core_ripple_material_light">#1f000000</color><color name="androidx_core_secondary_text_default_material_light">#8a000000</color><color name="background_floating_material_dark">@color/material_grey_800</color><color name="background_floating_material_light">@android:color/white</color><color name="background_material_dark">@color/material_grey_850</color><color name="background_material_light">@color/material_grey_50</color><color name="bright_foreground_disabled_material_dark">#80ffffff</color><color name="bright_foreground_disabled_material_light">#80000000</color><color name="bright_foreground_inverse_material_dark">@color/bright_foreground_material_light</color><color name="bright_foreground_inverse_material_light">@color/bright_foreground_material_dark</color><color name="bright_foreground_material_dark">@android:color/white</color><color name="bright_foreground_material_light">@android:color/black</color><color name="button_material_dark">#ff5a595b</color><color name="button_material_light">#ffd6d7d7</color><color name="cardview_dark_background">#ff424242</color><color name="cardview_light_background">#ffffffff</color><color name="cardview_shadow_end_color">#03000000</color><color name="cardview_shadow_start_color">#37000000</color><color name="colorAccent">#0250d4</color><color name="colorDanger">#cc0000</color><color name="colorPrimary">#2493ff</color><color name="colorPrimaryDark">#0250d4</color><color name="design_bottom_navigation_shadow_color">#14000000</color><color name="design_dark_default_color_background">#121212</color><color name="design_dark_default_color_error">#cf6679</color><color name="design_dark_default_color_on_background">#ffffff</color><color name="design_dark_default_color_on_error">#000000</color><color name="design_dark_default_color_on_primary">#000000</color><color name="design_dark_default_color_on_secondary">#000000</color><color name="design_dark_default_color_on_surface">#ffffff</color><color name="design_dark_default_color_primary">#ba86fc</color><color name="design_dark_default_color_primary_dark">#000000</color><color name="design_dark_default_color_primary_variant">#3700b3</color><color name="design_dark_default_color_secondary">#03dac6</color><color name="design_dark_default_color_secondary_variant">#03dac6</color><color name="design_dark_default_color_surface">#121212</color><color name="design_default_color_background">#ffffff</color><color name="design_default_color_error">#b00020</color><color name="design_default_color_on_background">#000000</color><color name="design_default_color_on_error">#ffffff</color><color name="design_default_color_on_primary">#ffffff</color><color name="design_default_color_on_secondary">#000000</color><color name="design_default_color_on_surface">#000000</color><color name="design_default_color_primary">#6200ee</color><color name="design_default_color_primary_dark">#3700b3</color><color name="design_default_color_primary_variant">#3700b3</color><color name="design_default_color_secondary">#03dac6</color><color name="design_default_color_secondary_variant">#018786</color><color name="design_default_color_surface">#ffffff</color><color name="design_fab_shadow_end_color">@android:color/transparent</color><color name="design_fab_shadow_mid_color">#14000000</color><color name="design_fab_shadow_start_color">#44000000</color><color name="design_fab_stroke_end_inner_color">#0a000000</color><color name="design_fab_stroke_end_outer_color">#0f000000</color><color name="design_fab_stroke_top_inner_color">#1affffff</color><color name="design_fab_stroke_top_outer_color">#2effffff</color><color name="design_snackbar_background_color">#323232</color><color name="dim_foreground_disabled_material_dark">#80bebebe</color><color name="dim_foreground_disabled_material_light">#80323232</color><color name="dim_foreground_material_dark">#ffbebebe</color><color name="dim_foreground_material_light">#ff323232</color><color name="error_color_material_dark">#ff7043</color><color name="error_color_material_light">#ff5722</color><color name="foreground_material_dark">@android:color/white</color><color name="foreground_material_light">@android:color/black</color><color name="highlighted_text_material_dark">#6680cbc4</color><color name="highlighted_text_material_light">#66009688</color><color name="ic_launcher_background">#e72e05</color><color name="m3_ref_palette_black">#ff000000</color><color name="m3_ref_palette_error0">#ff000000</color><color name="m3_ref_palette_error10">#ff410e0b</color><color name="m3_ref_palette_error100">#ffffffff</color><color name="m3_ref_palette_error20">#ff601410</color><color name="m3_ref_palette_error30">#ff8c1d18</color><color name="m3_ref_palette_error40">#ffb3261e</color><color name="m3_ref_palette_error50">#ffdc362e</color><color name="m3_ref_palette_error60">#ffe46962</color><color name="m3_ref_palette_error70">#ffec928e</color><color name="m3_ref_palette_error80">#fff2b8b5</color><color name="m3_ref_palette_error90">#fff9dedc</color><color name="m3_ref_palette_error95">#fffceeee</color><color name="m3_ref_palette_error99">#fffffbf9</color><color name="m3_ref_palette_neutral0">#ff000000</color><color name="m3_ref_palette_neutral10">#ff1d1b20</color><color name="m3_ref_palette_neutral100">#ffffffff</color><color name="m3_ref_palette_neutral12">#ff211f26</color><color name="m3_ref_palette_neutral17">#ff2b2930</color><color name="m3_ref_palette_neutral20">#ff322f35</color><color name="m3_ref_palette_neutral22">#ff36343b</color><color name="m3_ref_palette_neutral24">#ff3b383e</color><color name="m3_ref_palette_neutral30">#ff48464c</color><color name="m3_ref_palette_neutral4">#ff0f0d13</color><color name="m3_ref_palette_neutral40">#ff605d64</color><color name="m3_ref_palette_neutral50">#ff79767d</color><color name="m3_ref_palette_neutral6">#ff141218</color><color name="m3_ref_palette_neutral60">#ff938f96</color><color name="m3_ref_palette_neutral70">#ffaea9b1</color><color name="m3_ref_palette_neutral80">#ffcac5cd</color><color name="m3_ref_palette_neutral87">#ffded8e1</color><color name="m3_ref_palette_neutral90">#ffe6e0e9</color><color name="m3_ref_palette_neutral92">#ffece6f0</color><color name="m3_ref_palette_neutral94">#fff3edf7</color><color name="m3_ref_palette_neutral95">#fff5eff7</color><color name="m3_ref_palette_neutral96">#fff7f2fa</color><color name="m3_ref_palette_neutral98">#fffef7ff</color><color name="m3_ref_palette_neutral99">#fffffbff</color><color name="m3_ref_palette_neutral_variant0">#ff000000</color><color name="m3_ref_palette_neutral_variant10">#ff1d1a22</color><color name="m3_ref_palette_neutral_variant100">#ffffffff</color><color name="m3_ref_palette_neutral_variant20">#ff322f37</color><color name="m3_ref_palette_neutral_variant30">#ff49454f</color><color name="m3_ref_palette_neutral_variant40">#ff605d66</color><color name="m3_ref_palette_neutral_variant50">#ff79747e</color><color name="m3_ref_palette_neutral_variant60">#ff938f99</color><color name="m3_ref_palette_neutral_variant70">#ffaea9b4</color><color name="m3_ref_palette_neutral_variant80">#ffcac4d0</color><color name="m3_ref_palette_neutral_variant90">#ffe7e0ec</color><color name="m3_ref_palette_neutral_variant95">#fff5eefa</color><color name="m3_ref_palette_neutral_variant99">#fffffbfe</color><color name="m3_ref_palette_primary0">#ff000000</color><color name="m3_ref_palette_primary10">#ff21005d</color><color name="m3_ref_palette_primary100">#ffffffff</color><color name="m3_ref_palette_primary20">#ff381e72</color><color name="m3_ref_palette_primary30">#ff4f378b</color><color name="m3_ref_palette_primary40">#ff6750a4</color><color name="m3_ref_palette_primary50">#ff7f67be</color><color name="m3_ref_palette_primary60">#ff9a82db</color><color name="m3_ref_palette_primary70">#ffb69df8</color><color name="m3_ref_palette_primary80">#ffd0bcff</color><color name="m3_ref_palette_primary90">#ffeaddff</color><color name="m3_ref_palette_primary95">#fff6edff</color><color name="m3_ref_palette_primary99">#fffffbfe</color><color name="m3_ref_palette_secondary0">#ff000000</color><color name="m3_ref_palette_secondary10">#ff1d192b</color><color name="m3_ref_palette_secondary100">#ffffffff</color><color name="m3_ref_palette_secondary20">#ff332d41</color><color name="m3_ref_palette_secondary30">#ff4a4458</color><color name="m3_ref_palette_secondary40">#ff625b71</color><color name="m3_ref_palette_secondary50">#ff7a7289</color><color name="m3_ref_palette_secondary60">#ff958da5</color><color name="m3_ref_palette_secondary70">#ffb0a7c0</color><color name="m3_ref_palette_secondary80">#ffccc2dc</color><color name="m3_ref_palette_secondary90">#ffe8def8</color><color name="m3_ref_palette_secondary95">#fff6edff</color><color name="m3_ref_palette_secondary99">#fffffbfe</color><color name="m3_ref_palette_tertiary0">#ff000000</color><color name="m3_ref_palette_tertiary10">#ff31111d</color><color name="m3_ref_palette_tertiary100">#ffffffff</color><color name="m3_ref_palette_tertiary20">#ff492532</color><color name="m3_ref_palette_tertiary30">#ff633b48</color><color name="m3_ref_palette_tertiary40">#ff7d5260</color><color name="m3_ref_palette_tertiary50">#ff986977</color><color name="m3_ref_palette_tertiary60">#ffb58392</color><color name="m3_ref_palette_tertiary70">#ffd29dac</color><color name="m3_ref_palette_tertiary80">#ffefb8c8</color><color name="m3_ref_palette_tertiary90">#ffffd8e4</color><color name="m3_ref_palette_tertiary95">#ffffecf1</color><color name="m3_ref_palette_tertiary99">#fffffbfa</color><color name="m3_ref_palette_white">#ffffffff</color><color name="m3_sys_color_dark_background">@color/m3_ref_palette_neutral6</color><color name="m3_sys_color_dark_error">@color/m3_ref_palette_error80</color><color name="m3_sys_color_dark_error_container">@color/m3_ref_palette_error30</color><color name="m3_sys_color_dark_inverse_on_surface">@color/m3_ref_palette_neutral20</color><color name="m3_sys_color_dark_inverse_primary">@color/m3_ref_palette_primary40</color><color name="m3_sys_color_dark_inverse_surface">@color/m3_ref_palette_neutral90</color><color name="m3_sys_color_dark_on_background">@color/m3_ref_palette_neutral90</color><color name="m3_sys_color_dark_on_error">@color/m3_ref_palette_error20</color><color name="m3_sys_color_dark_on_error_container">@color/m3_ref_palette_error90</color><color name="m3_sys_color_dark_on_primary">@color/m3_ref_palette_primary20</color><color name="m3_sys_color_dark_on_primary_container">@color/m3_ref_palette_primary90</color><color name="m3_sys_color_dark_on_secondary">@color/m3_ref_palette_secondary20</color><color name="m3_sys_color_dark_on_secondary_container">@color/m3_ref_palette_secondary90</color><color name="m3_sys_color_dark_on_surface">@color/m3_ref_palette_neutral90</color><color name="m3_sys_color_dark_on_surface_variant">@color/m3_ref_palette_neutral_variant80</color><color name="m3_sys_color_dark_on_tertiary">@color/m3_ref_palette_tertiary20</color><color name="m3_sys_color_dark_on_tertiary_container">@color/m3_ref_palette_tertiary90</color><color name="m3_sys_color_dark_outline">@color/m3_ref_palette_neutral_variant60</color><color name="m3_sys_color_dark_outline_variant">@color/m3_ref_palette_neutral_variant30</color><color name="m3_sys_color_dark_primary">@color/m3_ref_palette_primary80</color><color name="m3_sys_color_dark_primary_container">@color/m3_ref_palette_primary30</color><color name="m3_sys_color_dark_secondary">@color/m3_ref_palette_secondary80</color><color name="m3_sys_color_dark_secondary_container">@color/m3_ref_palette_secondary30</color><color name="m3_sys_color_dark_surface">@color/m3_ref_palette_neutral6</color><color name="m3_sys_color_dark_surface_bright">@color/m3_ref_palette_neutral24</color><color name="m3_sys_color_dark_surface_container">@color/m3_ref_palette_neutral12</color><color name="m3_sys_color_dark_surface_container_high">@color/m3_ref_palette_neutral17</color><color name="m3_sys_color_dark_surface_container_highest">@color/m3_ref_palette_neutral22</color><color name="m3_sys_color_dark_surface_container_low">@color/m3_ref_palette_neutral10</color><color name="m3_sys_color_dark_surface_container_lowest">@color/m3_ref_palette_neutral4</color><color name="m3_sys_color_dark_surface_dim">@color/m3_ref_palette_neutral6</color><color name="m3_sys_color_dark_surface_variant">@color/m3_ref_palette_neutral_variant30</color><color name="m3_sys_color_dark_tertiary">@color/m3_ref_palette_tertiary80</color><color name="m3_sys_color_dark_tertiary_container">@color/m3_ref_palette_tertiary30</color><color name="m3_sys_color_light_background">@color/m3_ref_palette_neutral98</color><color name="m3_sys_color_light_error">@color/m3_ref_palette_error40</color><color name="m3_sys_color_light_error_container">@color/m3_ref_palette_error90</color><color name="m3_sys_color_light_inverse_on_surface">@color/m3_ref_palette_neutral95</color><color name="m3_sys_color_light_inverse_primary">@color/m3_ref_palette_primary80</color><color name="m3_sys_color_light_inverse_surface">@color/m3_ref_palette_neutral20</color><color name="m3_sys_color_light_on_background">@color/m3_ref_palette_neutral10</color><color name="m3_sys_color_light_on_error">@color/m3_ref_palette_error100</color><color name="m3_sys_color_light_on_error_container">@color/m3_ref_palette_error10</color><color name="m3_sys_color_light_on_primary">@color/m3_ref_palette_primary100</color><color name="m3_sys_color_light_on_primary_container">@color/m3_ref_palette_primary10</color><color name="m3_sys_color_light_on_secondary">@color/m3_ref_palette_secondary100</color><color name="m3_sys_color_light_on_secondary_container">@color/m3_ref_palette_secondary10</color><color name="m3_sys_color_light_on_surface">@color/m3_ref_palette_neutral10</color><color name="m3_sys_color_light_on_surface_variant">@color/m3_ref_palette_neutral_variant30</color><color name="m3_sys_color_light_on_tertiary">@color/m3_ref_palette_tertiary100</color><color name="m3_sys_color_light_on_tertiary_container">@color/m3_ref_palette_tertiary10</color><color name="m3_sys_color_light_outline">@color/m3_ref_palette_neutral_variant50</color><color name="m3_sys_color_light_outline_variant">@color/m3_ref_palette_neutral_variant80</color><color name="m3_sys_color_light_primary">@color/m3_ref_palette_primary40</color><color name="m3_sys_color_light_primary_container">@color/m3_ref_palette_primary90</color><color name="m3_sys_color_light_secondary">@color/m3_ref_palette_secondary40</color><color name="m3_sys_color_light_secondary_container">@color/m3_ref_palette_secondary90</color><color name="m3_sys_color_light_surface">@color/m3_ref_palette_neutral98</color><color name="m3_sys_color_light_surface_bright">@color/m3_ref_palette_neutral98</color><color name="m3_sys_color_light_surface_container">@color/m3_ref_palette_neutral94</color><color name="m3_sys_color_light_surface_container_high">@color/m3_ref_palette_neutral92</color><color name="m3_sys_color_light_surface_container_highest">@color/m3_ref_palette_neutral90</color><color name="m3_sys_color_light_surface_container_low">@color/m3_ref_palette_neutral96</color><color name="m3_sys_color_light_surface_container_lowest">@color/m3_ref_palette_neutral100</color><color name="m3_sys_color_light_surface_dim">@color/m3_ref_palette_neutral87</color><color name="m3_sys_color_light_surface_variant">@color/m3_ref_palette_neutral_variant90</color><color name="m3_sys_color_light_tertiary">@color/m3_ref_palette_tertiary40</color><color name="m3_sys_color_light_tertiary_container">@color/m3_ref_palette_tertiary90</color><color name="m3_sys_color_on_primary_fixed">@color/m3_ref_palette_primary10</color><color name="m3_sys_color_on_primary_fixed_variant">@color/m3_ref_palette_primary30</color><color name="m3_sys_color_on_secondary_fixed">@color/m3_ref_palette_secondary10</color><color name="m3_sys_color_on_secondary_fixed_variant">@color/m3_ref_palette_secondary30</color><color name="m3_sys_color_on_tertiary_fixed">@color/m3_ref_palette_tertiary10</color><color name="m3_sys_color_on_tertiary_fixed_variant">@color/m3_ref_palette_tertiary30</color><color name="m3_sys_color_primary_fixed">@color/m3_ref_palette_primary90</color><color name="m3_sys_color_primary_fixed_dim">@color/m3_ref_palette_primary80</color><color name="m3_sys_color_secondary_fixed">@color/m3_ref_palette_secondary90</color><color name="m3_sys_color_secondary_fixed_dim">@color/m3_ref_palette_secondary80</color><color name="m3_sys_color_tertiary_fixed">@color/m3_ref_palette_tertiary90</color><color name="m3_sys_color_tertiary_fixed_dim">@color/m3_ref_palette_tertiary80</color><color name="material_blue_grey_800">#ff37474f</color><color name="material_blue_grey_900">#ff263238</color><color name="material_blue_grey_950">#ff21272b</color><color name="material_deep_teal_200">#ff80cbc4</color><color name="material_deep_teal_500">#ff008577</color><color name="material_grey_100">#fff5f5f5</color><color name="material_grey_300">#ffe0e0e0</color><color name="material_grey_50">#fffafafa</color><color name="material_grey_600">#ff757575</color><color name="material_grey_800">#ff424242</color><color name="material_grey_850">#ff303030</color><color name="material_grey_900">#ff212121</color><color name="material_harmonized_color_error">#ffffff</color><color name="material_harmonized_color_error_container">#ffffff</color><color name="material_harmonized_color_on_error">#ffffff</color><color name="material_harmonized_color_on_error_container">#ffffff</color><color name="material_personalized_color_background">#ffffff</color><color name="material_personalized_color_control_activated">#ffffff</color><color name="material_personalized_color_control_highlight">#ffffff</color><color name="material_personalized_color_control_normal">#ffffff</color><color name="material_personalized_color_error">#ffffff</color><color name="material_personalized_color_error_container">#ffffff</color><color name="material_personalized_color_on_background">#ffffff</color><color name="material_personalized_color_on_error">#ffffff</color><color name="material_personalized_color_on_error_container">#ffffff</color><color name="material_personalized_color_on_primary">#ffffff</color><color name="material_personalized_color_on_primary_container">#ffffff</color><color name="material_personalized_color_on_secondary">#ffffff</color><color name="material_personalized_color_on_secondary_container">#ffffff</color><color name="material_personalized_color_on_surface">#ffffff</color><color name="material_personalized_color_on_surface_inverse">#ffffff</color><color name="material_personalized_color_on_surface_variant">#ffffff</color><color name="material_personalized_color_on_tertiary">#ffffff</color><color name="material_personalized_color_on_tertiary_container">#ffffff</color><color name="material_personalized_color_outline">#ffffff</color><color name="material_personalized_color_outline_variant">#ffffff</color><color name="material_personalized_color_primary">#ffffff</color><color name="material_personalized_color_primary_container">#ffffff</color><color name="material_personalized_color_primary_inverse">#ffffff</color><color name="material_personalized_color_secondary">#ffffff</color><color name="material_personalized_color_secondary_container">#ffffff</color><color name="material_personalized_color_surface">#ffffff</color><color name="material_personalized_color_surface_bright">#ffffff</color><color name="material_personalized_color_surface_container">#ffffff</color><color name="material_personalized_color_surface_container_high">#ffffff</color><color name="material_personalized_color_surface_container_highest">#ffffff</color><color name="material_personalized_color_surface_container_low">#ffffff</color><color name="material_personalized_color_surface_container_lowest">#ffffff</color><color name="material_personalized_color_surface_dim">#ffffff</color><color name="material_personalized_color_surface_inverse">#ffffff</color><color name="material_personalized_color_surface_variant">#ffffff</color><color name="material_personalized_color_tertiary">#ffffff</color><color name="material_personalized_color_tertiary_container">#ffffff</color><color name="material_personalized_color_text_hint_foreground_inverse">#ffffff</color><color name="material_personalized_color_text_primary_inverse">#ffffff</color><color name="material_personalized_color_text_primary_inverse_disable_only">#ffffff</color><color name="material_personalized_color_text_secondary_and_tertiary_inverse">#ffffff</color><color name="material_personalized_color_text_secondary_and_tertiary_inverse_disabled">#ffffff</color><color name="mtrl_btn_text_color_disabled">#61000000</color><color name="mtrl_btn_transparent_bg_color">#00ffffff</color><color name="mtrl_scrim_color">#52000000</color><color name="mtrl_textinput_default_box_stroke_color">#6b000000</color><color name="mtrl_textinput_disabled_color">#1f000000</color><color name="mtrl_textinput_filled_box_default_background_color">#0a000000</color><color name="mtrl_textinput_focused_box_stroke_color">#00000000</color><color name="mtrl_textinput_hovered_box_stroke_color">#de000000</color><color name="notification_action_color_filter">#ffffffff</color><color name="notification_icon_bg_color">#ff9e9e9e</color><color name="primary_dark_material_dark">@android:color/black</color><color name="primary_dark_material_light">@color/material_grey_600</color><color name="primary_material_dark">@color/material_grey_900</color><color name="primary_material_light">@color/material_grey_100</color><color name="primary_text_default_material_dark">#ffffffff</color><color name="primary_text_default_material_light">#de000000</color><color name="primary_text_disabled_material_dark">#4dffffff</color><color name="primary_text_disabled_material_light">#39000000</color><color name="ripple_material_dark">#33ffffff</color><color name="ripple_material_light">#1f000000</color><color name="secondary_text_default_material_dark">#b3ffffff</color><color name="secondary_text_default_material_light">#8a000000</color><color name="secondary_text_disabled_material_dark">#36ffffff</color><color name="secondary_text_disabled_material_light">#24000000</color><color name="switch_thumb_disabled_material_dark">#ff616161</color><color name="switch_thumb_disabled_material_light">#ffbdbdbd</color><color name="switch_thumb_normal_material_dark">#ffbdbdbd</color><color name="switch_thumb_normal_material_light">#fff1f1f1</color><color name="tooltip_background_dark">#e6616161</color><color name="tooltip_background_light">#e6ffffff</color></file><file path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">MTC SMS FORWARDING </string><string name="abc_action_bar_home_description">Navigate home</string><string name="abc_action_bar_up_description">Navigate up</string><string name="abc_action_menu_overflow_description">More options</string><string name="abc_action_mode_done">Done</string><string name="abc_activity_chooser_view_see_all">See all</string><string name="abc_activitychooserview_choose_application">Choose an app</string><string name="abc_capital_off">OFF</string><string name="abc_capital_on">ON</string><string name="abc_menu_alt_shortcut_label">Alt+</string><string name="abc_menu_ctrl_shortcut_label">Ctrl+</string><string name="abc_menu_delete_shortcut_label">delete</string><string name="abc_menu_enter_shortcut_label">enter</string><string name="abc_menu_function_shortcut_label">Function+</string><string name="abc_menu_meta_shortcut_label">Meta+</string><string name="abc_menu_shift_shortcut_label">Shift+</string><string name="abc_menu_space_shortcut_label">space</string><string name="abc_menu_sym_shortcut_label">Sym+</string><string name="abc_prepend_shortcut_label">Menu+</string><string name="abc_search_hint">Search…</string><string name="abc_searchview_description_clear">Clear query</string><string name="abc_searchview_description_query">Search query</string><string name="abc_searchview_description_search">Search</string><string name="abc_searchview_description_submit">Submit query</string><string name="abc_searchview_description_voice">Voice search</string><string name="abc_shareactionprovider_share_with">Share with</string><string name="abc_shareactionprovider_share_with_application">Share with %s</string><string name="abc_toolbar_collapse_description">Collapse</string><string name="androidx_startup">androidx.startup</string><string name="any">Any</string><string name="appbar_scrolling_view_behavior">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string><string name="asterisk">*</string><string name="bottom_sheet_behavior">com.google.android.material.bottomsheet.BottomSheetBehavior</string><string name="bottomsheet_action_collapse">Collapse the bottom sheet</string><string name="bottomsheet_action_expand">Expand the bottom sheet</string><string name="bottomsheet_action_expand_halfway">Expand halfway</string><string name="bottomsheet_drag_handle_clicked">Drag handle double-tapped</string><string name="bottomsheet_drag_handle_content_description">Drag handle</string><string name="btn_add">Add</string><string name="btn_cancel">Cancel</string><string name="btn_delete">Delete</string><string name="character_counter_content_description">Characters entered %1$d of %2$d</string><string name="character_counter_overflowed_content_description">Character limit exceeded %1$d of %2$d</string><string name="character_counter_pattern">%1$d/%2$d</string><string name="clear_text_end_icon_content_description">Clear text</string><string name="confirm_delete">Do you really want to delete %s record?</string><string name="delete_record">Delete record</string><string name="dest_title">destination title</string><string name="error_a11y_label">Error: invalid</string><string name="error_empty_sender">Empty sender</string><string name="error_empty_url">Empty URL</string><string name="error_icon_content_description">Error</string><string name="error_wrong_json">Not a valid JSON object</string><string name="error_wrong_url">Wrong URL</string><string name="exposed_dropdown_menu_content_description">Show dropdown menu</string><string name="fab_transformation_scrim_behavior">com.google.android.material.transformation.FabTransformationScrimBehavior</string><string name="fab_transformation_sheet_behavior">com.google.android.material.transformation.FabTransformationSheetBehavior</string><string name="hide_bottom_view_on_scroll_behavior">com.google.android.material.behavior.HideBottomViewOnScrollBehavior</string><string name="hint_sender">number or text</string><string name="icon_content_description">Dialog Icon</string><string name="input_phone">66123456789</string><string name="input_url">https://example.com</string><string name="item_view_role_description">Tab</string><string formatted="false" name="json_template_recommendation">Available placeholders %text%, %from%, %sentStamp%, %receivedStamp%, %sim%</string><string name="key_phones_preference">phones</string><string name="label_add_sender">Sender (number or text)</string><string name="label_ignore_ssl">Ignore SSL/TLS certificate errors</string><string name="label_json_headers">Headers</string><string name="label_json_template">Json Payload Template</string><string name="label_sender">Sender</string><string name="label_url">Webhook URL</string><string name="m3_exceed_max_badge_text_suffix">%1$s%2$s</string><string name="m3_ref_typeface_brand_medium">sans-serif-medium</string><string name="m3_ref_typeface_brand_regular">sans-serif</string><string name="m3_ref_typeface_plain_medium">sans-serif-medium</string><string name="m3_ref_typeface_plain_regular">sans-serif</string><string name="m3_sys_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string><string name="m3_sys_motion_easing_emphasized_accelerate">cubic-bezier(0.3, 0, 0.8, 0.2)</string><string name="m3_sys_motion_easing_emphasized_decelerate">cubic-bezier(0.1, 0.7, 0.1, 1)</string><string name="m3_sys_motion_easing_emphasized_path_data">M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1</string><string name="m3_sys_motion_easing_legacy">cubic-bezier(0.4, 0, 0.2, 1)</string><string name="m3_sys_motion_easing_legacy_accelerate">cubic-bezier(0.4, 0, 1, 1)</string><string name="m3_sys_motion_easing_legacy_decelerate">cubic-bezier(0, 0, 0.2, 1)</string><string name="m3_sys_motion_easing_linear">cubic-bezier(0, 0, 1, 1)</string><string name="m3_sys_motion_easing_standard">cubic-bezier(0.2, 0, 0, 1)</string><string name="m3_sys_motion_easing_standard_accelerate">cubic-bezier(0.3, 0, 1, 1)</string><string name="m3_sys_motion_easing_standard_decelerate">cubic-bezier(0, 0, 0, 1)</string><string name="material_clock_display_divider">:</string><string name="material_clock_toggle_content_description">Select AM or PM</string><string name="material_hour_24h_suffix">%1$s hours</string><string name="material_hour_selection">Select hour</string><string name="material_hour_suffix">%1$s o\'clock</string><string name="material_minute_selection">Select minutes</string><string name="material_minute_suffix">%1$s minutes</string><string name="material_motion_easing_accelerated">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string><string name="material_motion_easing_decelerated">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string><string name="material_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string><string name="material_motion_easing_linear">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string><string name="material_motion_easing_standard">cubic-bezier(0.4, 0.0, 0.2, 1.0)</string><string name="material_slider_range_end">Range end</string><string name="material_slider_range_start">Range start</string><string name="material_slider_value">Value</string><string name="material_timepicker_am">AM</string><string name="material_timepicker_clock_mode_description">Switch to clock mode for the time input.</string><string name="material_timepicker_hour">Hour</string><string name="material_timepicker_minute">Minute</string><string name="material_timepicker_pm">PM</string><string name="material_timepicker_select_time">Select time</string><string name="material_timepicker_text_input_mode_description">Switch to text input mode for the time input.</string><string name="mtrl_badge_numberless_content_description">New notification</string><string name="mtrl_checkbox_button_icon_path_checked">M14,18.2 11.4,15.6 10,17 14,21 22,13 20.6,11.6z</string><string name="mtrl_checkbox_button_icon_path_group_name">icon</string><string name="mtrl_checkbox_button_icon_path_indeterminate">M13.4,15 11,15 11,17 13.4,17 21,17 21,15z</string><string name="mtrl_checkbox_button_icon_path_name">icon path</string><string name="mtrl_checkbox_button_path_checked">M23,7H9C7.9,7,7,7.9,7,9v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V9C25,7.9,24.1,7,23,7z</string><string name="mtrl_checkbox_button_path_group_name">button</string><string name="mtrl_checkbox_button_path_name">button path</string><string name="mtrl_checkbox_button_path_unchecked">M23,7H9C7.9,7,7,7.9,7,9v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V9C25,7.9,24.1,7,23,7z M23,23H9V9h14V23z</string><string name="mtrl_checkbox_state_description_checked">Checked</string><string name="mtrl_checkbox_state_description_indeterminate">Partially checked</string><string name="mtrl_checkbox_state_description_unchecked">Not checked</string><string name="mtrl_chip_close_icon_content_description">Remove %1$s</string><string name="mtrl_exceed_max_badge_number_content_description">More than %1$d new notifications</string><string name="mtrl_exceed_max_badge_number_suffix">%1$d%2$s</string><string name="mtrl_picker_a11y_next_month">Change to next month</string><string name="mtrl_picker_a11y_prev_month">Change to previous month</string><string name="mtrl_picker_announce_current_range_selection">Start date selection: %1$s – End date selection: %2$s</string><string name="mtrl_picker_announce_current_selection">Current selection: %1$s</string><string name="mtrl_picker_announce_current_selection_none">none</string><string name="mtrl_picker_cancel">Cancel</string><string name="mtrl_picker_confirm">OK</string><string name="mtrl_picker_date_header_selected">%1$s</string><string name="mtrl_picker_date_header_title">Select Date</string><string name="mtrl_picker_date_header_unselected">Selected date</string><string name="mtrl_picker_day_of_week_column_header">%1$s</string><string name="mtrl_picker_end_date_description">End date %1$s</string><string name="mtrl_picker_invalid_format">Invalid format.</string><string name="mtrl_picker_invalid_format_example">Example: %1$s</string><string name="mtrl_picker_invalid_format_use">Use: %1$s</string><string name="mtrl_picker_invalid_range">Invalid range.</string><string name="mtrl_picker_navigate_to_current_year_description">Navigate to current year %1$d</string><string name="mtrl_picker_navigate_to_year_description">Navigate to year %1$d</string><string name="mtrl_picker_out_of_range">Out of range: %1$s</string><string name="mtrl_picker_range_header_only_end_selected">Start date – %1$s</string><string name="mtrl_picker_range_header_only_start_selected">%1$s – End date</string><string name="mtrl_picker_range_header_selected">%1$s – %2$s</string><string name="mtrl_picker_range_header_title">Select Range</string><string name="mtrl_picker_range_header_unselected">Start date – End date</string><string name="mtrl_picker_save">Save</string><string name="mtrl_picker_start_date_description">Start date %1$s</string><string name="mtrl_picker_text_input_date_hint">Date</string><string name="mtrl_picker_text_input_date_range_end_hint">End date</string><string name="mtrl_picker_text_input_date_range_start_hint">Start date</string><string name="mtrl_picker_text_input_day_abbr">d</string><string name="mtrl_picker_text_input_month_abbr">m</string><string name="mtrl_picker_text_input_year_abbr">y</string><string name="mtrl_picker_today_description">Today %1$s</string><string name="mtrl_picker_toggle_to_calendar_input_mode">Switch to calendar input mode</string><string name="mtrl_picker_toggle_to_day_selection">Tap to switch to Calendar view</string><string name="mtrl_picker_toggle_to_text_input_mode">Switch to text input mode</string><string name="mtrl_picker_toggle_to_year_selection">Tap to switch to year view</string><string name="mtrl_switch_thumb_group_name">circle_group</string><string name="mtrl_switch_thumb_path_checked">M4,16 A12,12 0 0,1 16,4 H16 A12,12 0 0,1 16,28 H16 A12,12 0 0,1 4,16</string><string name="mtrl_switch_thumb_path_morphing">M0,16 A11,11 0 0,1 11,5 H21 A11,11 0 0,1 21,27 H11 A11,11 0 0,1 0,16</string><string name="mtrl_switch_thumb_path_name">circle</string><string name="mtrl_switch_thumb_path_pressed">M2,16 A14,14 0 0,1 16,2 H16 A14,14 0 0,1 16,30 H16 A14,14 0 0,1 2,16</string><string name="mtrl_switch_thumb_path_unchecked">M8,16 A8,8 0 0,1 16,8 H16 A8,8 0 0,1 16,24 H16 A8,8 0 0,1 8,16</string><string name="mtrl_switch_track_decoration_path">M1,16 A15,15 0 0,1 16,1 H36 A15,15 0 0,1 36,31 H16 A15,15 0 0,1 1,16</string><string name="mtrl_switch_track_path">M0,16 A16,16 0 0,1 16,0 H36 A16,16 0 0,1 36,32 H16 A16,16 0 0,1 0,16</string><string name="mtrl_timepicker_cancel">Cancel</string><string name="mtrl_timepicker_confirm">OK</string><string name="nav_app_bar_navigate_up_description">Navigate up</string><string name="nav_app_bar_open_drawer_description">Open navigation drawer</string><string name="notification_channel">Default</string><string name="password_toggle_content_description">Show password</string><string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string><string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string><string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string><string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string><string name="permission_needed">Set permission to access SMS to use this app.</string><string name="search_menu_title">Search</string><string name="searchbar_scrolling_view_behavior">com.google.android.material.search.SearchBar$ScrollingViewBehavior</string><string name="searchview_clear_text_content_description">Clear text</string><string name="searchview_navigation_content_description">Back</string><string name="sender_recommendation">Use * symbol to catch any SMS</string><string name="side_sheet_accessibility_pane_title">Side Sheet</string><string name="side_sheet_behavior">com.google.android.material.sidesheet.SideSheetBehavior</string><string name="status_bar_notification_info_overflow">999+</string><string name="add">add</string><string name="notification_permission_needed">Notification permission is required for real-time updates.</string><string name="pending_notifications">Pending</string><string name="total_notifications">Total</string><string name="no_notifications">No notifications yet</string><string name="tab_webhooks">Webhooks</string><string name="filter_success">Success Only</string><string name="failed_notifications">Failed</string><string name="retry_notifications">Retrying</string><string name="filter_retry">Retry Only</string><string name="tab_monitor">Monitor</string><string name="logs_cleared">Notification logs cleared</string><string name="filter_failed">Failed Only</string><string name="no_notifications_desc">Webhook notifications will appear here</string><string name="filter_all">All Status</string><string name="filter_pending">Pending Only</string><string name="refresh">Refresh</string><string name="successful_notifications">Success</string><string name="clear_logs">Clear</string><string name="notification_statistics">Notification Statistics</string><string name="confirm_clear_logs">Are you sure you want to clear all notification logs?</string></file><file path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.MtcAddMoney" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.MtcAddMoney" parent="Base.Theme.MtcAddMoney"/></file><file path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.MtcAddMoney" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="dialog_add" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\layout\dialog_add.xml" qualifiers="" type="layout"/><file name="list_item" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\layout\list_item.xml" qualifiers="" type="layout"/><file name="background_gradient" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\drawable\background_gradient.xml" qualifiers="" type="drawable"/><file path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\values\styles.xml" qualifiers=""><style name="HintTextAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
    </style></file><file name="ic_launcher_foreground" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="status_badge_failed" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\drawable\status_badge_failed.xml" qualifiers="" type="drawable"/><file name="status_badge_pending" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\drawable\status_badge_pending.xml" qualifiers="" type="drawable"/><file name="status_badge_retry" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\drawable\status_badge_retry.xml" qualifiers="" type="drawable"/><file name="status_badge_success" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\drawable\status_badge_success.xml" qualifiers="" type="drawable"/><file name="activity_main_with_tabs" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\layout\activity_main_with_tabs.xml" qualifiers="" type="layout"/><file name="fragment_monitor" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\layout\fragment_monitor.xml" qualifiers="" type="layout"/><file name="fragment_webhooks" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\layout\fragment_webhooks.xml" qualifiers="" type="layout"/><file name="item_notification_log" path="C:\xampp\htdocs\MtcAddMoney\app\src\main\res\layout\item_notification_log.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\MtcAddMoney\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\MtcAddMoney\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\MtcAddMoney\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\xampp\htdocs\MtcAddMoney\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>