package com.mtc.addmoney;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

/**
 * Fragment for managing webhook configurations
 */
public class WebhooksFragment extends Fragment {

    private ListView listView;
    private ListAdapter listAdapter;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_webhooks, container, false);
        
        listView = view.findViewById(R.id.listView);
        setupListView();
        
        return view;
    }

    private void setupListView() {
        listAdapter = new ListAdapter(ForwardingConfig.getAll(requireContext()), requireContext());
        listView.setAdapter(listAdapter);
    }

    /**
     * Get the list adapter for external access (e.g., from MainActivity)
     */
    public ListAdapter getListAdapter() {
        return listAdapter;
    }

    /**
     * Refresh the webhook list
     */
    public void refreshList() {
        if (listAdapter != null) {
            listAdapter.clear();
            listAdapter.addAll(ForwardingConfig.getAll(requireContext()));
            listAdapter.notifyDataSetChanged();
        }
    }
}
