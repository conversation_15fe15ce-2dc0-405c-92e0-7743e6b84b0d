<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/list_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingBottom="8dp">

    <!-- Sender Label -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/label_sender"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/text_sender"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#000000"
        android:textSize="18sp" />

    <!-- URL Label -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/label_url"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/text_url"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#000000"
        android:textSize="18sp" />

    <!-- JSON Template Label -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/label_json_template"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/text_template"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#000000"
        android:textSize="11sp"
        android:typeface="monospace" />

    <!-- JSON Headers Label -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/label_json_headers"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/text_headers"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#000000"
        android:textSize="11sp"
        android:typeface="monospace" />

    <!-- Delete Button -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="8dp">

        <Button
            android:id="@+id/delete_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:minHeight="32dp"
            android:text="@string/btn_delete"
            android:textAllCaps="true"
            android:textColor="@color/colorDanger"
            android:textSize="14sp"
            style="@style/Widget.MaterialComponents.Button.TextButton" />
    </RelativeLayout>

</LinearLayout>
