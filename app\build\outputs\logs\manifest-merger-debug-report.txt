-- Merging decision tree log ---
manifest
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:2:1-200:12
INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:2:1-200:12
INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:2:1-200:12
INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:2:1-200:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f7566d407a97696d5c58add443eebd\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33932ee0bd67ae271f61579964b13003\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5f56202bc0edcc2d1323c4784d4da2d\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97f871a1db6fc59707f351b418b00543\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba12843e5c311a2b099568970217bd64\transformed\recyclerview-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc3499cba330563d0e756de82e68981\transformed\viewpager2-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c9aa147bece490a79fc4cb6b2a1f27e\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2aeea9d14a8e6a2ab39eb1cd6b0e72bd\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime-ktx:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f273a0132ad896d1063c3fea4d130d7\transformed\work-runtime-ktx-2.10.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\014ccd63c950c677520a0d6250ad1a75\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab799c451393405db09589944c8833ce\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa96c11e6ecc1e81d9c162e8372fc20c\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2247f39cdaf8f7d750fe3ce017fdd104\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840d895986a080f789155a88e1a3c4db\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8eb67da3835d17ee6a9ea4f54afeb33\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61e8c2a578766dc997b16d35b6436c68\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0000e0932da1f3fee5d3e8757314508d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0781d268f73aa8df22f436fc84e1cd78\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef54e67ad9a7bbe7cb1e79798c2c5ff\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f24badbb8ac6a754ce7f04a5c7420b2a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d96b63fdf6934dc53eaaf0890f1f8f1\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d3ad830af325d6aa407a644a2331e74\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f27ddad2f2f516a15b1c9cfc580153f\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d3adb25a221fd75e56ab65f0121988\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9af184753564d94b09332c2f703633d7\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5df19a73c4583e9ca081c3e5a019cb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\318617be802f53e1c15039173a0cfc70\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69f7d9b6f35d926877c15d120a9f04fb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1640adb1ec6324be5272b2f8e4cc94eb\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c48a386e608498d965efc178d33beb3b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1663b84c96cae069f57fb9fbaaa36f84\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db275300bc914af32ce96ca32e87fe46\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f92e3f90de62ba58e754ca7202eabe8\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d977bc618fb655514c04099f530bf8d\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55399c800fdbabaf6c50960dbbaebcb6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1ff40fb439ae8da1adc70612a1a60a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\722dca45c365bdeabb919772c4924006\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26fe398aaab8a068b5b8755270aa4b60\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7683c8a99e06fc7505141b3b3bcad163\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa2524f9f1f9a49e2fbe84ca0ed00f64\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a63521bcdd73c1d9b963eb18833ffc\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54b06323511b1a536138ecd331429fa2\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0983f1470b61eb51263ef1544e18942\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cebc7fe4632a47b8dc4b3816c7634be\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bec4a57384450d2e4a322f3a9caf0fa6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec5da8a5945a73957804dc65db68bca1\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea57445171cbce964cef0c1094c3f200\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b73af6bbe97c420ca6c4a9efe191e2e\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7c640c406191ccc51a287936105541e\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\220b8dd91074dc0fda1e71b049024817\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:24:5-79
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.RECEIVE_SMS
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:7:5-70
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:7:22-67
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:8:5-77
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:8:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:9:5-81
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:10:5-68
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:23:5-68
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:10:22-65
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:11:5-77
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:11:22-74
permission#com.mtc.addmoney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:12:5-14:47
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d977bc618fb655514c04099f530bf8d\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:14:9-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:13:9-81
uses-feature#android.hardware.telephony
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:15:5-17:36
	android:required
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:17:9-33
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:16:9-50
uses-permission#com.mtc.addmoney.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:18:5-97
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d977bc618fb655514c04099f530bf8d\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:18:22-94
application
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:19:5-198:19
INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:19:5-198:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f7566d407a97696d5c58add443eebd\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f7566d407a97696d5c58add443eebd\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33932ee0bd67ae271f61579964b13003\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33932ee0bd67ae271f61579964b13003\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840d895986a080f789155a88e1a3c4db\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840d895986a080f789155a88e1a3c4db\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f24badbb8ac6a754ce7f04a5c7420b2a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f24badbb8ac6a754ce7f04a5c7420b2a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d977bc618fb655514c04099f530bf8d\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d977bc618fb655514c04099f530bf8d\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1ff40fb439ae8da1adc70612a1a60a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1ff40fb439ae8da1adc70612a1a60a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\722dca45c365bdeabb919772c4924006\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\722dca45c365bdeabb919772c4924006\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7683c8a99e06fc7505141b3b3bcad163\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7683c8a99e06fc7505141b3b3bcad163\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
	android:extractNativeLibs
		INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml
	android:supportsRtl
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:26:9-35
	android:label
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:24:9-41
	android:appComponentFactory
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:28:9-77
	android:fullBackupContent
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:22:9-54
	android:roundIcon
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:25:9-54
	tools:targetApi
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:30:9-29
	android:icon
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:23:9-43
	android:allowBackup
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:20:9-35
	android:theme
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:27:9-49
	android:dataExtractionRules
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:21:9-65
	android:usesCleartextTraffic
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:29:9-44
activity#com.mtc.addmoney.MainActivity
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:34:9-41:20
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:36:13-36
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:35:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:37:13-40:29
action#android.intent.action.MAIN
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:38:17-69
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:38:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:39:17-77
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:39:27-74
service#com.mtc.addmoney.SmsReceiverService
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:44:9-47:39
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:46:13-35
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:47:13-36
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:45:13-63
receiver#com.mtc.addmoney.BootCompletedReceiver
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:50:9-57:20
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:52:13-35
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:53:13-36
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:51:13-66
intent-filter#action:name:android.intent.action.BOOT_COMPLETED
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:54:13-56:29
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:55:17-79
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:55:25-76
provider#androidx.startup.InitializationProvider
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:60:9-68:20
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840d895986a080f789155a88e1a3c4db\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840d895986a080f789155a88e1a3c4db\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f24badbb8ac6a754ce7f04a5c7420b2a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f24badbb8ac6a754ce7f04a5c7420b2a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1ff40fb439ae8da1adc70612a1a60a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1ff40fb439ae8da1adc70612a1a60a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	android:authorities
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:63:13-68
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:62:13-37
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:61:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:64:13-111
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:34:13-36:52
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:64:76-108
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:64:24-75
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:65:13-118
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840d895986a080f789155a88e1a3c4db\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840d895986a080f789155a88e1a3c4db\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:65:83-115
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:65:24-82
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:66:13-121
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f24badbb8ac6a754ce7f04a5c7420b2a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f24badbb8ac6a754ce7f04a5c7420b2a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:66:86-118
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:66:24-85
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:67:13-128
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:67:93-125
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:67:24-92
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:70:9-74:47
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:39:9-45:35
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:72:13-72
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:73:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:74:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:71:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:76:9-81:47
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:46:9-52:35
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:79:13-70
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:80:13-36
	android:permission
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:78:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:81:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:77:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:83:9-87:47
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:53:9-59:35
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:85:13-77
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:86:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:87:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:84:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable.BroadcastReceiver
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:90:9-95:50
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:92:13-35
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:93:13-37
	tools:ignore
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:95:13-47
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:94:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:91:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryChargingProxy
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:97:9-106:20
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:99:13-36
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:100:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:101:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:98:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:102:13-105:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:103:17-87
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:103:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:104:17-90
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:104:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy.BatteryNotLowProxy
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:108:9-117:20
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:110:13-36
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:111:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:112:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:109:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:113:13-116:29
action#android.intent.action.BATTERY_OKAY
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:114:17-77
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:114:25-74
action#android.intent.action.BATTERY_LOW
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:115:17-76
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:115:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy.StorageNotLowProxy
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:119:9-128:20
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:121:13-36
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:122:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:123:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:120:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:124:13-127:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:125:17-83
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:125:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:126:17-82
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:126:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy.NetworkStateProxy
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:130:9-138:20
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:132:13-36
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:133:13-37
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:134:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:131:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:135:13-137:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:136:17-79
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:136:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:140:9-150:20
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:110:9-121:20
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:142:13-36
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:143:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:144:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:141:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:145:13-149:29
action#android.intent.action.TIME_SET
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:147:17-73
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:147:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:148:17-81
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:148:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:152:9-160:20
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:122:9-131:20
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:154:13-72
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:155:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:156:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:153:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:157:13-159:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:158:17-98
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:158:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:162:9-171:20
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:132:9-142:20
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:165:13-35
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:166:13-36
	android:permission
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:164:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:167:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:163:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:168:13-170:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:169:17-88
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:169:25-85
uses-library#androidx.window.extensions
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:174:9-92
	android:required
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:174:65-89
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:174:23-64
uses-library#androidx.window.sidecar
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:175:9-89
	android:required
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:175:62-86
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:175:23-61
service#androidx.room.MultiInstanceInvalidationService
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:178:9-181:46
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7683c8a99e06fc7505141b3b3bcad163\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7683c8a99e06fc7505141b3b3bcad163\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:180:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7683c8a99e06fc7505141b3b3bcad163\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:181:13-43
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:179:13-74
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:184:9-196:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:187:13-35
	android:exported
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:188:13-36
	android:permission
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:186:13-57
	android:directBootAware
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:189:13-44
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:185:13-76
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION+action:name:androidx.profileinstaller.action.INSTALL_PROFILE+action:name:androidx.profileinstaller.action.SAVE_PROFILE+action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:190:13-195:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:191:17-91
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:191:25-88
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:192:17-85
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:192:25-82
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:193:17-88
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:193:25-85
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:194:17-95
	android:name
		ADDED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml:194:25-92
uses-sdk
INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml
INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f7566d407a97696d5c58add443eebd\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\37f7566d407a97696d5c58add443eebd\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33932ee0bd67ae271f61579964b13003\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\33932ee0bd67ae271f61579964b13003\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5f56202bc0edcc2d1323c4784d4da2d\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5f56202bc0edcc2d1323c4784d4da2d\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97f871a1db6fc59707f351b418b00543\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97f871a1db6fc59707f351b418b00543\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba12843e5c311a2b099568970217bd64\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba12843e5c311a2b099568970217bd64\transformed\recyclerview-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc3499cba330563d0e756de82e68981\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3cc3499cba330563d0e756de82e68981\transformed\viewpager2-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c9aa147bece490a79fc4cb6b2a1f27e\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c9aa147bece490a79fc4cb6b2a1f27e\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2aeea9d14a8e6a2ab39eb1cd6b0e72bd\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2aeea9d14a8e6a2ab39eb1cd6b0e72bd\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f273a0132ad896d1063c3fea4d130d7\transformed\work-runtime-ktx-2.10.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f273a0132ad896d1063c3fea4d130d7\transformed\work-runtime-ktx-2.10.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\014ccd63c950c677520a0d6250ad1a75\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\014ccd63c950c677520a0d6250ad1a75\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab799c451393405db09589944c8833ce\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab799c451393405db09589944c8833ce\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa96c11e6ecc1e81d9c162e8372fc20c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa96c11e6ecc1e81d9c162e8372fc20c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2247f39cdaf8f7d750fe3ce017fdd104\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2247f39cdaf8f7d750fe3ce017fdd104\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840d895986a080f789155a88e1a3c4db\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\840d895986a080f789155a88e1a3c4db\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8eb67da3835d17ee6a9ea4f54afeb33\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8eb67da3835d17ee6a9ea4f54afeb33\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61e8c2a578766dc997b16d35b6436c68\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\61e8c2a578766dc997b16d35b6436c68\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0000e0932da1f3fee5d3e8757314508d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0000e0932da1f3fee5d3e8757314508d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0781d268f73aa8df22f436fc84e1cd78\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0781d268f73aa8df22f436fc84e1cd78\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef54e67ad9a7bbe7cb1e79798c2c5ff\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef54e67ad9a7bbe7cb1e79798c2c5ff\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f24badbb8ac6a754ce7f04a5c7420b2a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f24badbb8ac6a754ce7f04a5c7420b2a\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d96b63fdf6934dc53eaaf0890f1f8f1\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d96b63fdf6934dc53eaaf0890f1f8f1\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d3ad830af325d6aa407a644a2331e74\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d3ad830af325d6aa407a644a2331e74\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f27ddad2f2f516a15b1c9cfc580153f\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f27ddad2f2f516a15b1c9cfc580153f\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d3adb25a221fd75e56ab65f0121988\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54d3adb25a221fd75e56ab65f0121988\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9af184753564d94b09332c2f703633d7\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9af184753564d94b09332c2f703633d7\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5df19a73c4583e9ca081c3e5a019cb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5df19a73c4583e9ca081c3e5a019cb\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\318617be802f53e1c15039173a0cfc70\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\318617be802f53e1c15039173a0cfc70\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69f7d9b6f35d926877c15d120a9f04fb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\69f7d9b6f35d926877c15d120a9f04fb\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1640adb1ec6324be5272b2f8e4cc94eb\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1640adb1ec6324be5272b2f8e4cc94eb\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c48a386e608498d965efc178d33beb3b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c48a386e608498d965efc178d33beb3b\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1663b84c96cae069f57fb9fbaaa36f84\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1663b84c96cae069f57fb9fbaaa36f84\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db275300bc914af32ce96ca32e87fe46\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\db275300bc914af32ce96ca32e87fe46\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f92e3f90de62ba58e754ca7202eabe8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f92e3f90de62ba58e754ca7202eabe8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d977bc618fb655514c04099f530bf8d\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d977bc618fb655514c04099f530bf8d\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55399c800fdbabaf6c50960dbbaebcb6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55399c800fdbabaf6c50960dbbaebcb6\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1ff40fb439ae8da1adc70612a1a60a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1ff40fb439ae8da1adc70612a1a60a7\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\722dca45c365bdeabb919772c4924006\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\722dca45c365bdeabb919772c4924006\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26fe398aaab8a068b5b8755270aa4b60\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26fe398aaab8a068b5b8755270aa4b60\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7683c8a99e06fc7505141b3b3bcad163\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7683c8a99e06fc7505141b3b3bcad163\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa2524f9f1f9a49e2fbe84ca0ed00f64\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aa2524f9f1f9a49e2fbe84ca0ed00f64\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a63521bcdd73c1d9b963eb18833ffc\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a63521bcdd73c1d9b963eb18833ffc\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54b06323511b1a536138ecd331429fa2\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\54b06323511b1a536138ecd331429fa2\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0983f1470b61eb51263ef1544e18942\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0983f1470b61eb51263ef1544e18942\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cebc7fe4632a47b8dc4b3816c7634be\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4cebc7fe4632a47b8dc4b3816c7634be\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bec4a57384450d2e4a322f3a9caf0fa6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bec4a57384450d2e4a322f3a9caf0fa6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec5da8a5945a73957804dc65db68bca1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ec5da8a5945a73957804dc65db68bca1\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea57445171cbce964cef0c1094c3f200\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ea57445171cbce964cef0c1094c3f200\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b73af6bbe97c420ca6c4a9efe191e2e\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b73af6bbe97c420ca6c4a9efe191e2e\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7c640c406191ccc51a287936105541e\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c7c640c406191ccc51a287936105541e\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\220b8dd91074dc0fda1e71b049024817\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\220b8dd91074dc0fda1e71b049024817\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\xampp\htdocs\MtcAddMoney\app\src\main\AndroidManifest.xml
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:68:13-106
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:79:13-104
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:90:13-104
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9cd548bedabd327ab853222a7eb37dc\transformed\work-runtime-2.10.2\AndroidManifest.xml:101:13-103
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d977bc618fb655514c04099f530bf8d\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d977bc618fb655514c04099f530bf8d\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\619e68b3922c02b3ddb654d953b68ebc\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
